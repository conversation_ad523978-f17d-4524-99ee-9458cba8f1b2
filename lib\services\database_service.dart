import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:azkar_app/models/zikr.dart';

class DatabaseService {
  static Database? _database;
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final path = join(await getDatabasesPath(), 'azkar_database.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE azkar(
            id TEXT PRIMARY KEY,
            text TEXT,
            count INTEGER,
            currentCount INTEGER,
            completed INTEGER,
            category TEXT,
            isCustom INTEGER
          )
        ''');
        
        // إدخال البيانات الأولية
        await _insertInitialData(db);
      },
    );
  }
  
  Future<void> _insertInitialData(Database db) async {
    // أذكار الصباح
    final morningAzkar = [
      {
        'id': '1',
        'text': 'أَعُوذُ بِاللهِ مِنْ الشَّيْطَانِ الرَّجِيمِ\nاللّهُ لاَ إِلَـهَ إِلاَّ هُوَ الْحَيُّ الْقَيُّومُ لاَ تَأْخُذُهُ سِنَةٌ وَلاَ نَوْمٌ...',
        'count': 1,
        'category': 'morning',
      },
      {
        'id': '2',
        'text': 'أَصْبَحْنَا وَأَصْبَحَ الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ...',
        'count': 1,
        'category': 'morning',
      },
      // إضافة المزيد من أذكار الصباح
    ];
    
    // أذكار المساء
    final eveningAzkar = [
      {
        'id': '101',
        'text': 'أَمْسَيْنَا وَأَمْسَى الْمُلْكُ لِلَّهِ، وَالْحَمْدُ لِلَّهِ، لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ...',
        'count': 1,
        'category': 'evening',
      },
      {
        'id': '102',
        'text': 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ، وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ...',
        'count': 1,
        'category': 'evening',
      },
      // إضافة المزيد من أذكار المساء
    ];
    
    // أذكار بعد الصلاة
    final afterPrayerAzkar = [
      {
        'id': '201',
        'text': 'أَسْتَغْفِرُ اللَّهَ (ثَلاثًا) اللَّهُمَّ أَنْتَ السَّلامُ، وَمِنْكَ السَّلامُ، تَبارَكْتَ يا ذَا الْجَلالِ وَالإكْرَامِ',
        'count': 1,
        'category': 'after_prayer',
      },
      {
        'id': '202',
        'text': 'لا إلهَ إلاّ اللّهُ وحدَهُ لا شريكَ لهُ، لهُ المُلكُ ولهُ الحَمدُ، وهوَ على كلّ شيءٍ قديرٌ...',
        'count': 1,
        'category': 'after_prayer',
      },
      // إضافة المزيد من أذكار بعد الصلاة
    ];
    
    // إدخال البيانات
    for (final zikr in [...morningAzkar, ...eveningAzkar, ...afterPrayerAzkar]) {
      await db.insert('azkar', {
        'id': zikr['id'],
        'text': zikr['text'],
        'count': zikr['count'],
        'currentCount': 0,
        'completed': 0,
        'category': zikr['category'],
        'isCustom': 0,
      });
    }
  }
  
  Future<List<Zikr>> getAzkarByCategory(String category) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'azkar',
      where: 'category = ?',
      whereArgs: [category],
    );
    
    return List.generate(maps.length, (i) => Zikr.fromMap(maps[i]));
  }
  
  Future<List<String>> getCategories() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      'SELECT DISTINCT category FROM azkar',
    );
    
    return List.generate(maps.length, (i) => maps[i]['category'] as String);
  }
  
  Future<void> updateZikr(Zikr zikr) async {
    final db = await database;
    await db.update(
      'azkar',
      zikr.toMap(),
      where: 'id = ?',
      whereArgs: [zikr.id],
    );
  }
  
  Future<void> addZikr(Zikr zikr) async {
    final db = await database;
    await db.insert('azkar', zikr.toMap());
  }
}