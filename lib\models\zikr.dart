class Zikr {
  final String id;
  final String text;
  final int count;
  int currentCount;
  bool completed;
  final String category;
  final bool isCustom;
  
  Zikr({
    required this.id,
    required this.text,
    required this.count,
    this.currentCount = 0,
    this.completed = false,
    required this.category,
    this.isCustom = false,
  });
  
  factory Zikr.fromMap(Map<String, dynamic> map) {
    return Zikr(
      id: map['id'],
      text: map['text'],
      count: map['count'],
      currentCount: map['currentCount'] ?? 0,
      completed: map['completed'] == 1,
      category: map['category'],
      isCustom: map['isCustom'] == 1,
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'count': count,
      'currentCount': currentCount,
      'completed': completed ? 1 : 0,
      'category': category,
      'isCustom': isCustom ? 1 : 0,
    };
  }
}