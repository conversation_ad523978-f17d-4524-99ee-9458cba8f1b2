import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  bool get isDarkMode {
    if (_themeMode == ThemeMode.system) {
      final brightness = SchedulerBinding.instance.platformDispatcher.platformBrightness;
      return brightness == Brightness.dark;
    }
    return _themeMode == ThemeMode.dark;
  }

  void toggleTheme() {
    _themeMode = isDarkMode ? ThemeMode.light : ThemeMode.dark;
    notifyListeners();
  }
  
  // تحديد الألوان بناءً على وقت اليوم
  Color getBackgroundColorByTime(TimeOfDay time) {
    final hour = time.hour;
    
    // وقت الفجر (4-6)
    if (hour >= 4 && hour < 6) {
      return const Color(0xFFE6F0FF); // أزرق فاتح
    }
    // وقت الشروق (6-8)
    else if (hour >= 6 && hour < 8) {
      return const Color(0xFFFFF0F5); // وردي فاتح
    }
    // وقت الضحى والظهر (8-15)
    else if (hour >= 8 && hour < 15) {
      return const Color(0xFFFFFAE6); // أصفر فاتح
    }
    // وقت العصر والغروب (15-18)
    else if (hour >= 15 && hour < 18) {
      return const Color(0xFFFFE0CC); // برتقالي فاتح
    }
    // وقت المغرب (18-20)
    else if (hour >= 18 && hour < 20) {
      return const Color(0xFFFFCCCC); // أحمر فاتح
    }
    // وقت العشاء والليل (20-4)
    else {
      return const Color(0xFFE6E6FA); // بنفسجي فاتح
    }
  }
  
  // ثيم النهار
  ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.teal,
      brightness: Brightness.light,
      fontFamily: 'Amiri',
      scaffoldBackgroundColor: Colors.white,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        bodyLarge: TextStyle(fontSize: 18),
        bodyMedium: TextStyle(fontSize: 16),
      ),
    );
  }
  
  // ثيم الليل
  ThemeData get darkTheme {
    return ThemeData(
      primarySwatch: Colors.teal,
      brightness: Brightness.dark,
      fontFamily: 'Amiri',
      scaffoldBackgroundColor: const Color(0xFF121212),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1E1E1E),
        foregroundColor: Colors.white,
        centerTitle: true,
        elevation: 0,
      ),
      textTheme: const TextTheme(
        headlineLarge: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
        headlineMedium: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Colors.white),
        bodyLarge: TextStyle(fontSize: 18, color: Colors.white),
        bodyMedium: TextStyle(fontSize: 16, color: Colors.white),
      ),
    );
  }
}