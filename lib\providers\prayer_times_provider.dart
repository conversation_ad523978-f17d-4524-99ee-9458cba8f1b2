import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:adhan/adhan.dart';
import 'package:intl/intl.dart';

class PrayerTimesProvider extends ChangeNotifier {
  PrayerTimes? _prayerTimes;
  String _location = '';
  DateTime _date = DateTime.now();
  String _nextPrayer = '';
  Duration _timeUntilNextPrayer = Duration.zero;
  Timer? _timer;

  PrayerTimes? get prayerTimes => _prayerTimes;
  String get location => _location;
  String get nextPrayer => _nextPrayer;
  Duration get timeUntilNextPrayer => _timeUntilNextPrayer;
  
  PrayerTimesProvider() {
    _initPrayerTimes();
    _startTimer();
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateNextPrayer();
    });
  }
  
  Future<void> _initPrayerTimes() async {
    try {
      final position = await _determinePosition();
      final coordinates = Coordinates(position.latitude, position.longitude);
      
      // الحصول على اسم المدينة
      final placemarks = await placemarkFromCoordinates(position.latitude, position.longitude);
      if (placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        _location = '${placemark.locality}, ${placemark.country}';
      }
      
      final params = CalculationMethod.egyptian.getParameters();
      params.madhab = Madhab.shafi;
      
      _prayerTimes = PrayerTimes(coordinates, _date, params);
      _updateNextPrayer();
      notifyListeners();
    } catch (e) {
      debugPrint('Error getting prayer times: $e');
    }
  }
  
  void _updateNextPrayer() {
    if (_prayerTimes == null) return;
    
    final now = DateTime.now();
    final prayers = {
      'الفجر': _prayerTimes!.fajr,
      'الشروق': _prayerTimes!.sunrise,
      'الظهر': _prayerTimes!.dhuhr,
      'العصر': _prayerTimes!.asr,
      'المغرب': _prayerTimes!.maghrib,
      'العشاء': _prayerTimes!.isha,
    };
    
    DateTime? nextPrayerTime;
    String nextPrayerName = '';
    
    for (final entry in prayers.entries) {
      if (entry.value.isAfter(now)) {
        nextPrayerTime = entry.value;
        nextPrayerName = entry.key;
        break;
      }
    }
    
    // إذا كانت جميع الصلوات لليوم الحالي قد انتهت، فإن الصلاة التالية هي فجر اليوم التالي
    if (nextPrayerTime == null) {
      final tomorrow = _date.add(const Duration(days: 1));
      final params = CalculationMethod.egyptian.getParameters();
      params.madhab = Madhab.shafi;
      
      final tomorrowPrayers = PrayerTimes(
        Coordinates(_prayerTimes!.coordinates.latitude, _prayerTimes!.coordinates.longitude),
        tomorrow,
        params,
      );
      
      nextPrayerTime = tomorrowPrayers.fajr;
      nextPrayerName = 'الفجر';
    }
    
    _nextPrayer = nextPrayerName;
    _timeUntilNextPrayer = nextPrayerTime!.difference(now);
    notifyListeners();
  }
  
  String formatTimeUntilNextPrayer() {
    final hours = _timeUntilNextPrayer.inHours;
    final minutes = _timeUntilNextPrayer.inMinutes % 60;
    final seconds = _timeUntilNextPrayer.inSeconds % 60;
    
    return '$hours:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
  
  String formatPrayerTime(DateTime time) {
    return DateFormat.jm().format(time);
  }
  
  Future<Position> _determinePosition() async {
    bool serviceEnabled;
    LocationPermission permission;
    
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return Future.error('Location services are disabled.');
    }
    
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return Future.error('Location permissions are denied');
      }
    }
    
    if (permission == LocationPermission.deniedForever) {
      return Future.error('Location permissions are permanently denied');
    }
    
    return await Geolocator.getCurrentPosition();
  }
}