import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:azkar_app/providers/theme_provider.dart';
import 'package:azkar_app/providers/prayer_times_provider.dart';
import 'package:azkar_app/widgets/prayer_times_card.dart';
import 'package:azkar_app/widgets/azkar_category_card.dart';
import 'package:azkar_app/screens/azkar_morning_screen.dart';
import 'package:azkar_app/screens/azkar_evening_screen.dart';
import 'package:azkar_app/screens/azkar_prayer_screen.dart';
import 'package:azkar_app/screens/tasbih_screen.dart';
import 'package:azkar_app/screens/dua_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final prayerTimesProvider = Provider.of<PrayerTimesProvider>(context);
    
    // تحديد لون الخلفية بناءً على وقت اليوم
    final currentTime = TimeOfDay.now();
    final backgroundColor = themeProvider.getBackgroundColorByTime(currentTime);
    
    return Scaffold(
      backgroundColor: themeProvider.isDarkMode ? null : backgroundColor,
      appBar: AppBar(
        title: const Text('أذكار المسلم'),
        actions: [
          IconButton(
            icon: Icon(themeProvider.isDarkMode ? Icons.light_mode : Icons.dark_mode),
            onPressed: () => themeProvider.toggleTheme(),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // بطاقة مواقيت الصلاة
              PrayerTimesCard(prayerTimesProvider: prayerTimesProvider),
              
              const SizedBox(height: 24),
              
              // عنوان الأقسام
              Text(
                'الأذكار',
                style: Theme.of(context).textTheme.headlineMedium,
                textAlign: TextAlign.right,
              ),
              
              const SizedBox(height: 16),
              
              // بطاقات الأقسام
              GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // أذكار الصباح
                  AzkarCategoryCard(
                    title: 'أذكار الصباح',
                    icon: Icons.wb_sunny,
                    color: Colors.amber,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (_) => const AzkarMorningScreen()),
                    ),
                  ),
                  
                  // أذكار المساء
                  AzkarCategoryCard(
                    title: 'أذكار المساء',
                    icon: Icons.nightlight_round,
                    color: Colors.indigo,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (_) => const AzkarEveningScreen()),
                    ),
                  ),
                  
                  // أذكار بعد الصلاة
                  AzkarCategoryCard(
                    title: 'أذكار الصلاة',
                    icon: Icons.mosque,
                    color: Colors.teal,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (_) => const AzkarPrayerScreen()),
                    ),
                  ),
                  
                  // السبحة الإلكترونية
                  AzkarCategoryCard(
                    title: 'السبحة',
                    icon: Icons.cyclone,
                    color: Colors.green,
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(builder: (_) => const TasbihScreen()),
                