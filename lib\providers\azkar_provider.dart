import 'package:flutter/material.dart';
import 'package:azkar_app/models/zikr.dart';
import 'package:azkar_app/services/database_service.dart';

class AzkarProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  
  List<Zikr> _morningAzkar = [];
  List<Zikr> _eveningAzkar = [];
  List<Zikr> _afterPrayerAzkar = [];
  Map<String, List<Zikr>> _categorizedAzkar = {};
  
  List<Zikr> get morningAzkar => _morningAzkar;
  List<Zikr> get eveningAzkar => _eveningAzkar;
  List<Zikr> get afterPrayerAzkar => _afterPrayerAzkar;
  Map<String, List<Zikr>> get categorizedAzkar => _categorizedAzkar;
  
  int _completedMorningAzkar = 0;
  int _completedEveningAzkar = 0;
  
  int get completedMorningAzkar => _completedMorningAzkar;
  int get completedEveningAzkar => _completedEveningAzkar;
  
  AzkarProvider() {
    _loadAzkar();
  }
  
  Future<void> _loadAzkar() async {
    _morningAzkar = await _databaseService.getAzkarByCategory('morning');
    _eveningAzkar = await _databaseService.getAzkarByCategory('evening');
    _afterPrayerAzkar = await _databaseService.getAzkarByCategory('after_prayer');
    
    // تحميل الأذكار المصنفة
    final categories = await _databaseService.getCategories();
    for (final category in categories) {
      _categorizedAzkar[category] = await _databaseService.getAzkarByCategory(category);
    }
    
    _updateCompletedAzkar();
    notifyListeners();
  }
  
  void _updateCompletedAzkar() {
    _completedMorningAzkar = _morningAzkar.where((zikr) => zikr.completed).length;
    _completedEveningAzkar = _eveningAzkar.where((zikr) => zikr.completed).length;
  }
  
  Future<void> incrementZikrCounter(Zikr zikr) async {
    if (zikr.currentCount < zikr.count) {
      zikr.currentCount++;
      
      if (zikr.currentCount == zikr.count) {
        zikr.completed = true;
        _updateCompletedAzkar();
      }
      
      await _databaseService.updateZikr(zikr);
      notifyListeners();
    }
  }
  
  Future<void> resetZikrCounter(Zikr zikr) async {
    zikr.currentCount = 0;
    zikr.completed = false;
    await _databaseService.updateZikr(zikr);
    _updateCompletedAzkar();
    notifyListeners();
  }
  
  Future<void> resetAllAzkar(String category) async {
    List<Zikr> azkarToReset = [];
    
    switch (category) {
      case 'morning':
        azkarToReset = _morningAzkar;
        break;
      case 'evening':
        azkarToReset = _eveningAzkar;
        break;
      case 'after_prayer':
        azkarToReset = _afterPrayerAzkar;
        break;
      default:
        if (_categorizedAzkar.containsKey(category)) {
          azkarToReset = _categorizedAzkar[category]!;
        }
    }
    
    for (final zikr in azkarToReset) {
      zikr.currentCount = 0;
      zikr.completed = false;
      await _databaseService.updateZikr(zikr);
    }
    
    _updateCompletedAzkar();
    notifyListeners();
  }
  
  Future<void> addCustomZikr(String text, int count, String category) async {
    final zikr = Zikr(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      text: text,
      count: count,
      currentCount: 0,
      completed: false,
      category: category,
      isCustom: true,
    );
    
    await _databaseService.addZikr(zikr);
    
    // إضافة الذكر إلى القائمة المناسبة
    switch (category) {
      case 'morning':
        _morningAzkar.add(zikr);
        break;
      case 'evening':
        _eveningAzkar.add(zikr);
        break;
      case 'after_prayer':
        _afterPrayerAzkar.add(zikr);
        break;
      default:
        if (_categorizedAzkar.containsKey(category)) {
          _categorizedAzkar[category]!.add(zikr);
        } else {
          _categorizedAzkar[category] = [zikr];
        }
    }
    
    notifyListeners();
  }
}